#include	"AD9912_LO3.h"
#include	"delay.h"


/*
*********************************************************************************************************
*	函 数 名: AD9912_IO_Init_LO3
*	功能说明: AD9912 引脚初始化
*	形    参：无
*	返 回 值: 无
*********************************************************************************************************
*/
void AD9912_IO_Init_LO3(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;

	__HAL_RCC_GPIOB_CLK_ENABLE();
	__HAL_RCC_GPIOE_CLK_ENABLE();

	//AD9912-SCK,SDI (PB10,PB11)
	GPIO_InitStructure.Pin = AD9912_SCK_LO3_PIN | AD9912_SDI_LO3_PIN;
	GPIO_InitStructure.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStructure.Pull = GPIO_NOPULL;
	GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(AD9912_SCK_LO3_PORT, &GPIO_InitStructure);

	//AD9912-CSB,UPDATA (PE15,PE13)
	GPIO_InitStructure.Pin = AD9912_CSB_LO3_PIN | AD9912_UPDATA_LO3_PIN;
	GPIO_InitStructure.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStructure.Pull = GPIO_NOPULL;
	GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(AD9912_CSB_LO3_PORT, &GPIO_InitStructure);
	
	//AD9912-SDO
//	GPIO_InitStructure.GPIO_Pin 	= GPIO_Pin_9;
//	GPIO_InitStructure.GPIO_Speed 	= GPIO_Speed_50MHz;
//	GPIO_InitStructure.GPIO_Mode 	= GPIO_Mode_IPD;						//下拉输入
//	GPIO_Init(GPIOB, &GPIO_InitStructure);
//	
//	//AD9912-CSB,UPDATA,RESET,PWRDOWN
//	GPIO_InitStructure.GPIO_Pin 	= GPIO_Pin_2 | GPIO_Pin_3 | GPIO_Pin_4 | GPIO_Pin_5;
//	GPIO_InitStructure.GPIO_Speed 	= GPIO_Speed_50MHz;
//	GPIO_InitStructure.GPIO_Mode 	= GPIO_Mode_Out_PP;					//推挽输出
//	GPIO_Init(GPIOE, &GPIO_InitStructure);
	
	AD9912_SCK_LO3_LOW();
	AD9912_SDI_LO3_LOW();
	AD9912_CSB_LO3_HIGH();
	AD9912_UPDATA_LO3_LOW();
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_write_8bit
*	功能说明: AD9912写入8bit数据
*	形    参：uint8_t p
*	返 回 值: 无
*********************************************************************************************************
*/
static void AD9912_write_8bit(uint8_t p)
{
	int i;
	for(i=0;i<8;i++)
	{
		if(p & 0x80)
			AD9912_SDI_LO3_HIGH();
		else
			AD9912_SDI_LO3_LOW();
		
		p=p<<1;
		delay_us(1);
		AD9912_SCK_LO3_LOW();
		delay_us(5);
		AD9912_SCK_LO3_HIGH();
	}
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_write_24bit
*	功能说明: AD9912写入24bit数据
*	形    参：uint32_t p
*	返 回 值: 无
*********************************************************************************************************
*/
static void AD9912_write_24bit(uint32_t p)
{
	int	i;
	AD9912_CSB_LO3_LOW();
	delay_us(1);
	for(i=0;i<24;i++)
	{
		if(p & 0x800000)
			AD9912_SDI_LO3_HIGH();
		else
			AD9912_SDI_LO3_LOW();
		p=p<<1;
		delay_us(1);
		AD9912_SCK_LO3_LOW();
		delay_us(5);
		AD9912_SCK_LO3_HIGH();
	}
	delay_us(1);
	AD9912_CSB_LO3_HIGH();
	AD9912_SDI_LO3_LOW();
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_write_32bit
*	功能说明: AD9912写入32bit数据
*	形    参：uint32_t p
*	返 回 值: 无
*********************************************************************************************************
*/
static void AD9912_write_32bit(uint32_t p)
{
	int i;
	AD9912_CSB_LO3_LOW();
	delay_us(1);
	for(i=0;i<32;i++)
	{
		if(p & 0x80000000)
			AD9912_SDI_LO3_HIGH();
		else
			AD9912_SDI_LO3_LOW();
		
		p=p<<1;
		delay_us(1);
		AD9912_SCK_LO3_LOW();
		delay_us(5);
		AD9912_SCK_LO3_HIGH();
	}
	delay_us(1);
	AD9912_CSB_LO3_HIGH();
	AD9912_SDI_LO3_LOW();
	AD9912_SCK_LO3_LOW();
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_write_1byte
*	功能说明: AD9912写入1byte数据
*	形    参：uint16_t addr,uint8_t dat
*	返 回 值: 无
*********************************************************************************************************
*/
static void AD9912_write_1byte(uint16_t addr,uint8_t dat)
{
	uint32_t data = 0x00;
	data = (addr & 0x1fff)<<8;
	data = data | dat;
	AD9912_write_24bit(data);
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_write_2byte
*	功能说明: AD9912写入1byte数据
*	形    参：uint16_t addr,uint8_t dat
*	返 回 值: 无
*********************************************************************************************************
*/
static void AD9912_write_2byte(uint16_t addr,uint16_t dat)
{
	uint16_t data = 0x00;
	data = (addr & 0x3fff)<<16;
	data = data | dat;
	AD9912_write_32bit(data);
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_write_FTW_Init
*	功能说明: 
*	形    参：uint16_t addr,uint8_t dat
*	返 回 值: 无
*********************************************************************************************************
*/
static void AD9912_write_FTW_Init(double Freq,double ddsconst)
{
	//数据高位先行，32位分别一位一位的发frq[0]为高frq[3]为低
	double		j;											//双精度浮点64位
	long long	kk;											//64位长整
	unsigned char *pResult;						//定义8位指针
	uint8_t frq[6];
	
	j=(double)ddsconst*Freq;
	kk=j;
	pResult = (unsigned char *)&kk;		//强制类型转换
	
	frq[0] = pResult[5];
	frq[1] = pResult[4];
	frq[2] = pResult[3];
	frq[3] = pResult[2];
	frq[4] = pResult[1];
	frq[5] = pResult[0];
	
	AD9912_CSB_LO3_HIGH();
	AD9912_CSB_LO3_LOW();
	delay_us(1);
	
	AD9912_write_8bit(0x61);
	AD9912_write_8bit(0xab);
	AD9912_write_8bit(frq[0]);
	AD9912_write_8bit(frq[1]);
	AD9912_write_8bit(frq[2]);
	AD9912_write_8bit(frq[3]);
	AD9912_write_8bit(frq[4]);
	AD9912_write_8bit(frq[5]);
	delay_us(2);
	
	AD9912_CSB_LO3_HIGH();
	
	AD9912_UPDATA_LO3_HIGH();
	AD9912_UPDATA_LO3_LOW();
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_write_ftw0
*	功能说明: 
*	形    参：uint64_t ftw0_val
*	返 回 值: 无
*********************************************************************************************************
*/
static void AD9912_write_ftw0(uint64_t ftw0_val)
{
	uint8_t *p;
	
	p = (uint8_t *)&ftw0_val;
	
	AD9912_CSB_LO3_LOW();
	delay_us(1);
	AD9912_write_8bit(0x61);
	AD9912_write_8bit(0xab);
	AD9912_write_8bit(p[5]);
	AD9912_write_8bit(p[4]);
	AD9912_write_8bit(p[3]);
	AD9912_write_8bit(p[2]);
	AD9912_write_8bit(p[1]);
	AD9912_write_8bit(p[0]);
	delay_us(1);
	AD9912_CSB_LO3_HIGH();
	
	AD9912_UPDATA_LO3_HIGH();
	AD9912_UPDATA_LO3_LOW();
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_get_ftw0
*	功能说明: 
*	形    参：double Fdds,double Fref
*	返 回 值: dds_ftw0
*********************************************************************************************************
*/
/*********************************************/
// FTW = round(2^48  * (Fdds/Fref))
// 单位 : HZ
// --yzq
/*********************************************/
static uint64_t AD9912_get_ftw0(double Fdds,double Fref)
{
	uint64_t dds_ftw0;
	double temp = 0;
	
	temp = Fdds/Fref;
	dds_ftw0 = (uint64_t)(temp*0xffffffffffff);
	
	return dds_ftw0;
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_Upfreq
*	功能说明: 
*	形    参：double freq
*	返 回 值: 无
*********************************************************************************************************
*/
void AD9912_Upfreq(double freq)
{
	uint64_t DDS_ftw0;
	
	DDS_ftw0 = AD9912_get_ftw0(freq,1000000000);
	AD9912_write_ftw0(DDS_ftw0);
}

/*
*********************************************************************************************************
*	函 数 名: AD9912_Init
*	功能说明: AD9912初始化
*	形    参：无
*	返 回 值: 无
*********************************************************************************************************
*/
void AD9912_Init()
{
	uint8_t i=0;
	
	AD9912_write_1byte(0x0000,0x18);
	AD9912_write_1byte(0x0010,0x90);
	AD9912_write_2byte(0x040c,0x0202);
	AD9912_Upfreq(98600000);						//98.6MHz
	delay_us(5000);
}
