#ifndef	__AD9912_LO2_H__
#define	__AD9912_LO2_H__

#include "stm32f1xx_hal.h"

#include "main.h"

//IO管脚宏定义
#define	AD9912_SCK_LO2_PIN			GPIO_PIN_11
#define	AD9912_SCK_LO2_PORT			GPIOE
#define AD9912_SDI_LO2_PIN			GPIO_PIN_9
#define AD9912_SDI_LO2_PORT			GPIOE
#define	AD9912_CSB_LO2_PIN			GPIO_PIN_7
#define	AD9912_CSB_LO2_PORT			GPIOE
#define AD9912_UPDATA_LO2_PIN		GPIO_PIN_1
#define AD9912_UPDATA_LO2_PORT		GPIOB

#define AD9912_SCK_LO2_HIGH()		HAL_GPIO_WritePin(AD9912_SCK_LO2_PORT, AD9912_SCK_LO2_PIN, GPIO_PIN_SET)
#define AD9912_SCK_LO2_LOW()		HAL_GPIO_WritePin(AD9912_SCK_LO2_PORT, AD9912_SCK_LO2_PIN, GPIO_PIN_RESET)
#define AD9912_SDI_LO2_HIGH()		HAL_GPIO_WritePin(AD9912_SDI_LO2_PORT, AD9912_SDI_LO2_PIN, GPIO_PIN_SET)
#define AD9912_SDI_LO2_LOW()		HAL_GPIO_WritePin(AD9912_SDI_LO2_PORT, AD9912_SDI_LO2_PIN, GPIO_PIN_RESET)
#define AD9912_CSB_LO2_HIGH()		HAL_GPIO_WritePin(AD9912_CSB_LO2_PORT, AD9912_CSB_LO2_PIN, GPIO_PIN_SET)
#define AD9912_CSB_LO2_LOW()		HAL_GPIO_WritePin(AD9912_CSB_LO2_PORT, AD9912_CSB_LO2_PIN, GPIO_PIN_RESET)
#define AD9912_UPDATA_LO2_HIGH()	HAL_GPIO_WritePin(AD9912_UPDATA_LO2_PORT, AD9912_UPDATA_LO2_PIN, GPIO_PIN_SET)
#define AD9912_UPDATA_LO2_LOW()		HAL_GPIO_WritePin(AD9912_UPDATA_LO2_PORT, AD9912_UPDATA_LO2_PIN, GPIO_PIN_RESET)

//#define AD9912_SDO_LO2				PBin(9)
//#define	AD9912_RESET_LO2			PEout(4)
//#define	AD9912_PWRDOWN_LO2			PEout(5)

//函数声明
void AD9912_IO_Init_LO2(void);
void AD9912_Upfreq_LO2(double freq);
void AD9912_Init_LO2();

#endif
