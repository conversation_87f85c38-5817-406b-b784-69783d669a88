#include "LMX2594_LO2.h"

/*	LMX2594-本振2引脚初始化	*/
void LMX2594_IO_Init_LO2(void) {
    GPIO_InitTypeDef GPIO_InitStructure;

    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    // PLL2_CLK
    GPIO_InitStructure.Pin = LMX2594_CLK_LO2_PIN;
    GPIO_InitStructure.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStructure.Pull = GPIO_NOPULL;
    GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(LMX2594_CLK_LO2_PORT, &GPIO_InitStructure);

    // PLL2_SDI、CS
    GPIO_InitStructure.Pin = LMX2594_SDI_LO2_PIN | LMX2594_CS_LO2_PIN;
    GPIO_InitStructure.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStructure.Pull = GPIO_NOPULL;
    GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(LMX2594_SDI_LO2_PORT, &GPIO_InitStructure);

    // PLL2_LD
    GPIO_InitStructure.Pin = LMX2594_LD_LO2_PIN;
    GPIO_InitStructure.Mode = GPIO_MODE_INPUT;
    GPIO_InitStructure.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(LMX2594_LD_LO2_PORT, &GPIO_InitStructure);
}

/*	LMX2594寄存器写操作	*/
void Write_Reg_LMX2594_LO2(uint8_t Addr, uint16_t data) {
    uint8_t i = 0;
    uint32_t Reg = 0, tmp = 0;

    Reg = (uint32_t)(((uint32_t)Addr << 16) | data);
    tmp = Reg;

    LMX2594_CS_LO2_HIGH();
    LMX2594_CLK_LO2_LOW();
    LMX2594_SDI_LO2_LOW();

    LMX2594_CS_LO2_LOW();
    LMX2594_CLK_LO2_LOW();

    for (i = 0; i < 24; i++) {
        if (tmp & 0x800000) {
            LMX2594_SDI_LO2_HIGH();
        } else {
            LMX2594_SDI_LO2_LOW();
        }

        LMX2594_CLK_LO2_HIGH();
        tmp = tmp << 1;
        LMX2594_CLK_LO2_LOW();
    }

    LMX2594_CS_LO2_HIGH();
    LMX2594_CLK_LO2_LOW();
    LMX2594_SDI_LO2_LOW();
}

/*	LMX2594基本寄存器初始化, */
/*	频率范围 9500 - 10700MHz,	默认9500MHz */
/*	参考频率 = 100MHz, 鉴相频率 = 200MHz, 步进频率 = 50MHz	*/
/*	当频率范围在 (nFpd-5,nFpd+5) 内,鉴相频率 = 175MHz */
void Set_Reg_LMX2594_LO2(uint32_t FreKHz, uint32_t RefFreKHz) {
    //	uint16_t R44_OUTA_PWR 	= 60;				//R44,OUTA_PWR
    //	uint16_t R14_CPG 		= 6;				//R14,CPG

    uint16_t R44_OUTA_PWR = 23;  // R44,OUTA_PWR
    uint16_t R14_CPG = 5;        // R14,CPG

    Write_Reg_LMX2594_LO2(0x00, 0x251E);  // R0,复位所有寄存器
    Write_Reg_LMX2594_LO2(0x00, 0x251C);  // R0

    Write_Reg_LMX2594_LO2(0x70, 0x0000);  // R112

    Write_Reg_LMX2594_LO2(0x6F, 0x0000);  // R111
    Write_Reg_LMX2594_LO2(0x6E, 0x0000);
    Write_Reg_LMX2594_LO2(0x6D, 0x0000);
    Write_Reg_LMX2594_LO2(0x6C, 0x0000);
    Write_Reg_LMX2594_LO2(0x6B, 0x0000);
    Write_Reg_LMX2594_LO2(0x6A, 0x0000);
    Write_Reg_LMX2594_LO2(0x69, 0x0021);
    Write_Reg_LMX2594_LO2(0x68, 0x0000);
    Write_Reg_LMX2594_LO2(0x67, 0x0000);
    Write_Reg_LMX2594_LO2(0x66, 0x3F00);
    Write_Reg_LMX2594_LO2(0x65, 0x0011);
    Write_Reg_LMX2594_LO2(0x64, 0x0000);
    Write_Reg_LMX2594_LO2(0x63, 0x0000);
    Write_Reg_LMX2594_LO2(0x62, 0x0400);
    Write_Reg_LMX2594_LO2(0x61, 0x0888);
    Write_Reg_LMX2594_LO2(0x60, 0x0000);  // R96

    Write_Reg_LMX2594_LO2(0x5F, 0x0000);  // R95
    Write_Reg_LMX2594_LO2(0x5E, 0x0000);
    Write_Reg_LMX2594_LO2(0x5D, 0x0000);
    Write_Reg_LMX2594_LO2(0x5C, 0x0000);
    Write_Reg_LMX2594_LO2(0x5B, 0x0000);
    Write_Reg_LMX2594_LO2(0x5A, 0x0000);
    Write_Reg_LMX2594_LO2(0x59, 0x0000);
    Write_Reg_LMX2594_LO2(0x58, 0x0000);
    Write_Reg_LMX2594_LO2(0x57, 0x0000);
    Write_Reg_LMX2594_LO2(0x56, 0x0000);
    Write_Reg_LMX2594_LO2(0x55, 0x9F00);
    Write_Reg_LMX2594_LO2(0x54, 0x0001);
    Write_Reg_LMX2594_LO2(0x53, 0x0000);
    Write_Reg_LMX2594_LO2(0x52, 0x3500);
    Write_Reg_LMX2594_LO2(0x51, 0x0000);
    Write_Reg_LMX2594_LO2(0x50, 0xCCCC);  // R80

    Write_Reg_LMX2594_LO2(0x4F, 0x004C);  // R79
    Write_Reg_LMX2594_LO2(0x4E, 0x0019);  // R78,	VCO_CAPCTRL_STRT = 12
    Write_Reg_LMX2594_LO2(0x4D, 0x0000);
    Write_Reg_LMX2594_LO2(0x4C, 0x000C);
    Write_Reg_LMX2594_LO2(0x4B, 0x0800);  // R75,	CHDIV = 0,(VCO分频)
    Write_Reg_LMX2594_LO2(0x4A, 0x0000);
    Write_Reg_LMX2594_LO2(0x49, 0x003F);
    Write_Reg_LMX2594_LO2(0x48, 0x0001);  // R78,	SYSREF_DIV = 1
    Write_Reg_LMX2594_LO2(0x47, 0x0081);  // R71,	SYSREF_DIV_PRE = 4
    Write_Reg_LMX2594_LO2(0x46, 0xC350);  // R70,	MASH_RST_COUNT[15:0],	MASH_RST_COUNT = 5000
    Write_Reg_LMX2594_LO2(0x45, 0x0000);  // R69,	MASH_RST_COUNT[31:16]
    Write_Reg_LMX2594_LO2(0x44, 0x03E8);
    Write_Reg_LMX2594_LO2(0x43, 0x0000);
    Write_Reg_LMX2594_LO2(0x42, 0x01F4);
    Write_Reg_LMX2594_LO2(0x41, 0x0000);
    Write_Reg_LMX2594_LO2(0x40, 0x1388);  // R64,

    Write_Reg_LMX2594_LO2(0x3F, 0x0000);  // R63,
    Write_Reg_LMX2594_LO2(0x3E, 0x0322);
    Write_Reg_LMX2594_LO2(0x3D, 0x00A8);
    Write_Reg_LMX2594_LO2(0x3C, 0x03E8);  // R60,	LD_DLY = 1000
    Write_Reg_LMX2594_LO2(0x3B, 0x0001);  // R59,	LD_TYPE = 1
    Write_Reg_LMX2594_LO2(0x3A, 0x8001);  // R58, 	INPIN_IGNORE = 1, INPIN_LVL = 0S
    Write_Reg_LMX2594_LO2(0x39, 0x0020);
    Write_Reg_LMX2594_LO2(0x38, 0x0000);
    Write_Reg_LMX2594_LO2(0x37, 0x0000);
    Write_Reg_LMX2594_LO2(0x36, 0x0000);
    Write_Reg_LMX2594_LO2(0x35, 0x0000);
    Write_Reg_LMX2594_LO2(0x34, 0x0820);
    Write_Reg_LMX2594_LO2(0x33, 0x0080);
    Write_Reg_LMX2594_LO2(0x32, 0x0000);
    Write_Reg_LMX2594_LO2(0x31, 0x4180);
    Write_Reg_LMX2594_LO2(0x30, 0x0300);  // R48,

    Write_Reg_LMX2594_LO2(0x2F, 0x0300);                          // R47,
    Write_Reg_LMX2594_LO2(0x2E, 0x07FC);                          // R46,	OUTB_MUX = 0
    Write_Reg_LMX2594_LO2(0x2D, 0xC8DF);                          // R45,	OUTA_MUX = 1(0:Channel divider, 1:VCO), OUTB_PWR = 0x1F
    Write_Reg_LMX2594_LO2(0x2C, 0x00A2 | ((R44_OUTA_PWR << 8)));  // R44,	OUTA_PWR = 15, OUTB_PD = 1, OUTA_PD = 0, MASH_RESET_N = 1
    Write_Reg_LMX2594_LO2(0x2B, 0x86A0);                          // R43,	PLL_NUM[15:0]	PLL_NUM = 100000, 分子
    Write_Reg_LMX2594_LO2(0x2A, 0x0001);                          // R42,	PLL_NUM[31:16]
    Write_Reg_LMX2594_LO2(0x29, 0x0000);
    Write_Reg_LMX2594_LO2(0x28, 0x0000);

    Write_Reg_LMX2594_LO2(0x27, 0x0D40);  // R39,	PLL_DEN[15:0],	PLL_DEN = 200000, 分母
    Write_Reg_LMX2594_LO2(0x26, 0x0003);  // R38,	PLL_DEN[31:16]
    Write_Reg_LMX2594_LO2(0x25, 0x0404);  // R37,	PFD_DLY_SEL = 4
    Write_Reg_LMX2594_LO2(0x24, 0x002F);  // R36,	PLL_N[15:0],	PLL_N = 47, N值
    Write_Reg_LMX2594_LO2(0x23, 0x0004);
    Write_Reg_LMX2594_LO2(0x22, 0x0000);  // R34,	PLL_N[18:16] = 0;
    Write_Reg_LMX2594_LO2(0x21, 0x1E21);
    Write_Reg_LMX2594_LO2(0x20, 0x0393);  // R32,

    Write_Reg_LMX2594_LO2(0x1F, 0x43EC);  // R31,
    Write_Reg_LMX2594_LO2(0x1E, 0x318C);
    Write_Reg_LMX2594_LO2(0x1D, 0x318C);
    Write_Reg_LMX2594_LO2(0x1C, 0x0488);
    Write_Reg_LMX2594_LO2(0x1B, 0x0002);
    Write_Reg_LMX2594_LO2(0x1A, 0x0DB0);
    Write_Reg_LMX2594_LO2(0x19, 0x0C2B);
    Write_Reg_LMX2594_LO2(0x18, 0x071A);
    Write_Reg_LMX2594_LO2(0x17, 0x007C);
    Write_Reg_LMX2594_LO2(0x16, 0x0001);
    Write_Reg_LMX2594_LO2(0x15, 0x0401);
    Write_Reg_LMX2594_LO2(0x14, 0xC848);  // R20,	VCO_SEL = 1, VCO_SEL_FORCE = 0
    Write_Reg_LMX2594_LO2(0x13, 0x270C);  // R19,
    Write_Reg_LMX2594_LO2(0x12, 0x0064);
    Write_Reg_LMX2594_LO2(0x11, 0x012C);  // R17,	VCO_DACISET_STRT = 300
    Write_Reg_LMX2594_LO2(0x10, 0x0080);  // R16

    Write_Reg_LMX2594_LO2(0x0F, 0x064F);                   // R15
    Write_Reg_LMX2594_LO2(0x0E, 0x1E00 | (R14_CPG << 4));  // R14,	CPG = 7,电荷泵增益 = 15 mA
    Write_Reg_LMX2594_LO2(0x0D, 0x4000);
    Write_Reg_LMX2594_LO2(0x0C, 0x5001);  // R12,	PLL_R_PRE = 1
    Write_Reg_LMX2594_LO2(0x0B, 0x0018);  // R11,	PLL_R = 1
    Write_Reg_LMX2594_LO2(0x0A, 0x10D8);  // R10, 	MULT = 1
    Write_Reg_LMX2594_LO2(0x09, 0x1604);  // R9, 	OSC_2X = 1
    Write_Reg_LMX2594_LO2(0x08, 0x2000);
    Write_Reg_LMX2594_LO2(0x07, 0x40B2);
    Write_Reg_LMX2594_LO2(0x06, 0xC802);
    Write_Reg_LMX2594_LO2(0x05, 0x00C8);
    Write_Reg_LMX2594_LO2(0x04, 0x0A43);
    Write_Reg_LMX2594_LO2(0x03, 0x0642);
    Write_Reg_LMX2594_LO2(0x02, 0x0500);
    Write_Reg_LMX2594_LO2(0x01, 0x0808);  // R1, CAL_CLK_DIV = 0
    Write_Reg_LMX2594_LO2(0x00, 0x251C);  // R0, MUXOUT_LD_SEL = 1(锁定指示),	Fpd = 200MHz

    Set_Fre_LMX2594_LO2(FreKHz, RefFreKHz);
}

/*	LMX2594基本寄存器初始化, */
/*	频率范围 9500 - 10700MHz,	默认9500MHz */
/*	参考频率 = 100MHz, 鉴相频率 = 200MHz, 步进频率 = 50MHz	*/
/*	当频率范围在 (nFpd-5,nFpd+5) 内,鉴相频率 = 175MHz */
void Set_Fre_LMX2594_LO2(uint32_t FreKHz, uint32_t RefFreKHz) {
    uint32_t FpdFreKHz = 0;  // 鉴相频率
    uint8_t OSC_2X = 0;      // 参考倍频
    uint16_t MULT = 0;       // 调制器
    uint16_t PLL_R = 0;      // 调制R分频
    uint16_t PLL_R_PRE = 0;  // 参考R分频
    uint16_t Divider = 1;    // VC0分频系数
    uint32_t FVcoKHz = 0;    // VCO频率,KHz
    uint32_t FVcoMHz = 0;    // VCO频率,MHz
    uint32_t PLL_N = 0;      // 整数
    uint32_t PLL_NUM = 0;    // 小数,分子
    double PLL_NUM_Tmp = 0;  // 小数,分子(临时变量)
    // uint32_t PLL_NUM_Tmp = 0;				//小数,分子(临时变量)
    uint32_t PLL_DEN = 100000;      // 小数,分母
    uint16_t VCO_CAPCTRT_STRT = 0;  // R78,部分辅助校准
    uint16_t VCO_DACISET_STRT = 0;  // R17,部分辅助校准
    uint16_t Quotient = 0;          // 整数
    uint32_t Remainder = 0;         // 余数

    //	uint16_t R44_OUTA_PWR 	= 60;				//R44,OUTA_PWR
    //	uint16_t R45_OUTB_PWR 	= 10;				//R45,OUTB_PWR

    uint16_t R44_OUTA_PWR = 23;  // R44,OUTA_PWR
    uint16_t R45_OUTB_PWR = 10;  // R45,OUTB_PWR

    uint16_t R14_CPG = 5;  // R14,CPG

    // VCO分频
    if ((FreKHz >= 7500000) && (FreKHz <= 15000000))  // 7.5 - 15 GHz
    {
        Divider = 1;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0800);
    } else if (FreKHz >= 7500000 / 2)  // 3.75 - 7.5 GHz, 2分频
    {
        Divider = 2;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x03EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0800);
    } else if (FreKHz >= 7500000 / 4)  // 1.875 - 3.75 GHz, 4分频
    {
        Divider = 4;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0840);
    } else if (FreKHz >= 7500000 / 6)  // 1.25 - 1.875 GHz, 6分频
    {
        Divider = 6;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0880);
    } else if (FreKHz >= 7500000 / 8)  // 0.9375 - 1.25 GHz, 8分频
    {
        Divider = 8;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x08C0);
    } else if (FreKHz >= 7500000 / 12)  // 0.625 - 0.9375 GHz, 12分频
    {
        Divider = 12;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0900);
    } else if (FreKHz >= 7500000 / 16)  // 0.46875 - 0.625 GHz, 16分频
    {
        Divider = 16;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0940);
    } else if (FreKHz >= 7500000 / 24)  // 0.3125 - 0.46875 GHz, 24分频
    {
        Divider = 24;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0980);
    } else if (FreKHz >= 7500000 / 32)  // 0.234375 - 0.3125 GHz, 32分频
    {
        Divider = 32;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x09C0);
    } else if (FreKHz >= 7500000 / 48)  // 0.15625 - 0.234375 GHz, 48分频
    {
        Divider = 48;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0A00);
    } else if (FreKHz >= 7500000 / 64)  // 0.1171875 - 0.15625 GHz, 64分频
    {
        Divider = 64;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0A40);
    } else if (FreKHz >= 7500000 / 72)  // 0.104167 - 0.1171875 GHz, 72分频
    {
        Divider = 72;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0A80);
    } else if (FreKHz >= 7500000 / 96)  // 0.078125 - 0.104167 GHz, 96分频
    {
        Divider = 96;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0AC0);
    } else if (FreKHz >= 7500000 / 128)  // 0.05859375 - 0.078125 GHz, 128分频
    {
        Divider = 128;
        FVcoKHz = FreKHz * Divider;

        Write_Reg_LMX2594_LO2(0x1F, 0x43EC);
        Write_Reg_LMX2594_LO2(0x4B, 0x0B00);
    }

    FVcoMHz = FVcoKHz / 1000;

    // 部分辅助校准
    if ((FVcoMHz >= 7500) && (FVcoMHz <= 8600))  // VCO1:7500 - 8600 MHz
    {
        Write_Reg_LMX2594_LO2(0x14, 0x0C48);

        VCO_CAPCTRT_STRT = 164 - (164 - 12) * (FVcoMHz - 7500) / (8600 - 7500);
        VCO_DACISET_STRT = 299 + (240 - 299) * (FVcoMHz - 7500) / (8600 - 7500);
    } else if ((FVcoMHz > 8600) && (FVcoMHz <= 9800))  // VCO2:8600 - 9800 MHz
    {
        Write_Reg_LMX2594_LO2(0x14, 0x1448);

        VCO_CAPCTRT_STRT = 165 - (165 - 16) * (FVcoMHz - 8600) / (9800 - 8600);
        VCO_DACISET_STRT = 356 + (247 - 356) * (FVcoMHz - 8600) / (9800 - 8600);
    } else if ((FVcoMHz > 9800) && (FVcoMHz <= 10800))  // VCO3:9800 - 10800 MHz
    {
        Write_Reg_LMX2594_LO2(0x14, 0x1C48);

        VCO_CAPCTRT_STRT = 158 - (158 - 19) * (FVcoMHz - 9800) / (10800 - 9800);
        VCO_DACISET_STRT = 324 + (224 - 324) * (FVcoMHz - 9800) / (10800 - 9800);
    } else if ((FVcoMHz > 10800) && (FVcoMHz <= 12000))  // VCO4:10800 - 12000 MHz
    {
        Write_Reg_LMX2594_LO2(0x14, 0x2448);

        VCO_CAPCTRT_STRT = 140 - (140 - 0) * (FVcoMHz - 10800) / (12000 - 10800);
        VCO_DACISET_STRT = 383 + (244 - 383) * (FVcoMHz - 10800) / (12000 - 10800);
    } else if ((FVcoMHz > 12000) && (FVcoMHz <= 12900))  // VCO5:12000 - 12900 MHz
    {
        Write_Reg_LMX2594_LO2(0x14, 0x2C48);

        VCO_CAPCTRT_STRT = 183 - (183 - 36) * (FVcoMHz - 12000) / (12900 - 12000);
        VCO_DACISET_STRT = 205 + (146 - 205) * (FVcoMHz - 12000) / (12900 - 12000);
    } else if ((FVcoMHz > 12900) && (FVcoMHz <= 13900))  // VCO6:12900 - 13900 MHz
    {
        Write_Reg_LMX2594_LO2(0x14, 0x3448);

        VCO_CAPCTRT_STRT = 155 - (155 - 6) * (FVcoMHz - 12900) / (13900 - 12900);
        VCO_DACISET_STRT = 242 + (163 - 242) * (FVcoMHz - 12900) / (13900 - 12900);
    } else if ((FVcoMHz > 13900) && (FVcoMHz <= 15000))  // VCO7:13900 - 15000 MHz
    {
        Write_Reg_LMX2594_LO2(0x14, 0x3C48);

        VCO_CAPCTRT_STRT = 175 - (175 - 19) * (FVcoMHz - 13900) / (15000 - 13900);
        VCO_DACISET_STRT = 323 + (244 - 323) * (FVcoMHz - 13900) / (15000 - 13900);
    }

    // 正常频率
    OSC_2X = 0;
    MULT = 1;
    PLL_R = 1;
    PLL_R_PRE = 1;
    // PLL_DEN = 1000000;
    PLL_DEN = 0xFFFFFFFF;

    // 鉴相频率
    FpdFreKHz = RefFreKHz * (OSC_2X + 1) * MULT / (PLL_R * PLL_R_PRE);

    PLL_N = FVcoKHz / (FpdFreKHz);
    PLL_NUM_Tmp = (FVcoKHz % FpdFreKHz * 1.0) / FpdFreKHz;
    PLL_NUM_Tmp = PLL_NUM_Tmp * (PLL_DEN);
    PLL_NUM = PLL_NUM_Tmp;

    if (FreKHz >= 7500000) {
        Write_Reg_LMX2594_LO2(0x2E, 0x07F1);  // R46,	OUTB_MUX = 1
        Write_Reg_LMX2594_LO2(0x2D, 0xCEDF);  // R45,	OUTA_MUX = 1(0:Channel divider, 1:VCO)
    } else {
        Write_Reg_LMX2594_LO2(0x2E, 0x07F0);  // R46,	OUTB_MUX = 0
        Write_Reg_LMX2594_LO2(0x2D, 0xC6DF);  // R45,	OUTA_MUX = 0(0:Channel divider, 1:VCO)
    }

    if ((FreKHz >= 12250000) && (FreKHz <= 12500000)) {
        R44_OUTA_PWR = 50;
    } else if ((FreKHz > 12500000) && (FreKHz <= 12950000)) {
        R44_OUTA_PWR = 28;
    } else if ((FreKHz > 12950000) && (FreKHz <= 13150000)) {
        R44_OUTA_PWR = 23;
    } else {
        R44_OUTA_PWR = 50;
    }

    R14_CPG = 4;  // R14,CPG

    Write_Reg_LMX2594_LO2(0x0E, 0x1E00 | (R14_CPG << 4));  // R14,	CPG = 7,电荷泵增益 = 15 mA

    Write_Reg_LMX2594_LO2(0x2C, 0x00A2 | ((R44_OUTA_PWR << 8)));  // R44,	OUTA_PWR = 15, OUTB_PD = 1, OUTA_PD = 0, MASH_RESET_N = 1

    Write_Reg_LMX2594_LO2(0x27, (PLL_DEN & 0x0000FFFF) >> 0);   // R39,	PLL_DEN[15:0],	PLL_DEN
    Write_Reg_LMX2594_LO2(0x26, (PLL_DEN & 0xFFFF0000) >> 16);  // R38,	PLL_DEN[31:16]

    Write_Reg_LMX2594_LO2(0x2B, (PLL_NUM & 0x0000FFFF) >> 0);   // R43,	PLL_NUM[15:0]	PLL_NUM
    Write_Reg_LMX2594_LO2(0x2A, (PLL_NUM & 0xFFFF0000) >> 16);  // R42,	PLL_NUM[31:16]
    Write_Reg_LMX2594_LO2(0x24, (PLL_N & 0x0000FFFF) >> 0);     // R36,	PLL_N[15:0],	PLL_N
    Write_Reg_LMX2594_LO2(0x22, ((PLL_N & 0xFFFF0000) >> 16));  // R34,	PLL_N[18:16]

    Write_Reg_LMX2594_LO2(0x0C, 0x5000 | PLL_R_PRE);       // R12,	PLL_R_PRE
    Write_Reg_LMX2594_LO2(0x0B, 0x0008 | (PLL_R << 4));    // R11,	PLL_R
    Write_Reg_LMX2594_LO2(0x0A, 0x1058 | (MULT << 7));     // R10, 	MULT
    Write_Reg_LMX2594_LO2(0x09, 0x0604 | (OSC_2X << 12));  // R9, 	OSC_2X

    if (FpdFreKHz > 200000) {
        Write_Reg_LMX2594_LO2(0x00, 0x259C);  // R0,Fpd > 200MHz
    } else if ((FpdFreKHz > 150000) && (FpdFreKHz <= 200000)) {
        Write_Reg_LMX2594_LO2(0x00, 0x251C);  // R0,150 < Fpd <= 200MHz
    } else if ((FpdFreKHz > 100000) && (FpdFreKHz <= 150000)) {
        Write_Reg_LMX2594_LO2(0x00, 0x249C);  // R0,100 < Fpd <= 150MHz
    } else if ((FpdFreKHz >= 10000) && (FpdFreKHz <= 100000)) {
        Write_Reg_LMX2594_LO2(0x00, 0x241C);  // R0,10 <= Fpd <= 100MHz
    } else {
        Write_Reg_LMX2594_LO2(0x00, 0x241C);  // R0,10 <= Fpd <= 100MHz
    }
}

/*	锁定指示检测		*/
uint8_t Check_LMX2594_LO2(void) {
    uint8_t check = 0;

    check = LMX2594_LD_LO2_READ();

    return check;
}
