#ifndef	__AD9912_LO1_H__
#define	__AD9912_LO1_H__

#include "stm32f1xx_hal.h"

#include "main.h"

//IO管脚宏定义
#define	AD9912_SCK_LO1_PIN			GPIO_PIN_8
#define	AD9912_SCK_LO1_PORT			GPIOA
#define AD9912_SDI_LO1_PIN			GPIO_PIN_9
#define AD9912_SDI_LO1_PORT			GPIOA
#define	AD9912_CSB_LO1_PIN			GPIO_PIN_10
#define	AD9912_CSB_LO1_PORT			GPIOA
#define AD9912_UPDATA_LO1_PIN		GPIO_PIN_11
#define AD9912_UPDATA_LO1_PORT		GPIOA

#define AD9912_SCK_LO1_HIGH()		HAL_GPIO_WritePin(AD9912_SCK_LO1_PORT, AD9912_SCK_LO1_PIN, GPIO_PIN_SET)
#define AD9912_SCK_LO1_LOW()		HAL_GPIO_WritePin(AD9912_SCK_LO1_PORT, AD9912_SCK_LO1_PIN, GPIO_PIN_RESET)
#define AD9912_SDI_LO1_HIGH()		HAL_GPIO_WritePin(AD9912_SDI_LO1_PORT, AD9912_SDI_LO1_PIN, GPIO_PIN_SET)
#define AD9912_SDI_LO1_LOW()		HAL_GPIO_WritePin(AD9912_SDI_LO1_PORT, AD9912_SDI_LO1_PIN, GPIO_PIN_RESET)
#define AD9912_CSB_LO1_HIGH()		HAL_GPIO_WritePin(AD9912_CSB_LO1_PORT, AD9912_CSB_LO1_PIN, GPIO_PIN_SET)
#define AD9912_CSB_LO1_LOW()		HAL_GPIO_WritePin(AD9912_CSB_LO1_PORT, AD9912_CSB_LO1_PIN, GPIO_PIN_RESET)
#define AD9912_UPDATA_LO1_HIGH()	HAL_GPIO_WritePin(AD9912_UPDATA_LO1_PORT, AD9912_UPDATA_LO1_PIN, GPIO_PIN_SET)
#define AD9912_UPDATA_LO1_LOW()		HAL_GPIO_WritePin(AD9912_UPDATA_LO1_PORT, AD9912_UPDATA_LO1_PIN, GPIO_PIN_RESET)

//#define AD9912_SDO_LO1				PBin(9)
//#define	AD9912_RESET_LO1			PEout(4)
//#define	AD9912_PWRDOWN_LO1			PEout(5)

//函数声明
void AD9912_IO_Init_LO1(void);
void AD9912_Upfreq(double freq);
void AD9912_Init();

#endif