#ifndef	__AD9912_LO3_H__
#define	__AD9912_LO3_H__

#include "stm32f1xx_hal.h"

#include "main.h"

//IO管脚宏定义
#define	AD9912_SCK_LO3_PIN			GPIO_PIN_10
#define	AD9912_SCK_LO3_PORT			GPIOB
#define AD9912_SDI_LO3_PIN			GPIO_PIN_11
#define AD9912_SDI_LO3_PORT			GPIOB
#define	AD9912_CSB_LO3_PIN			GPIO_PIN_15
#define	AD9912_CSB_LO3_PORT			GPIOE
#define AD9912_UPDATA_LO3_PIN		GPIO_PIN_13
#define AD9912_UPDATA_LO3_PORT		GPIOE

#define AD9912_SCK_LO3_HIGH()		HAL_GPIO_WritePin(AD9912_SCK_LO3_PORT, AD9912_SCK_LO3_PIN, GPIO_PIN_SET)
#define AD9912_SCK_LO3_LOW()		HAL_GPIO_WritePin(AD9912_SCK_LO3_PORT, AD9912_SCK_LO3_PIN, GPIO_PIN_RESET)
#define AD9912_SDI_LO3_HIGH()		HAL_GPIO_WritePin(AD9912_SDI_LO3_PORT, AD9912_SDI_LO3_PIN, GPIO_PIN_SET)
#define AD9912_SDI_LO3_LOW()		HAL_GPIO_WritePin(AD9912_SDI_LO3_PORT, AD9912_SDI_LO3_PIN, GPIO_PIN_RESET)
#define AD9912_CSB_LO3_HIGH()		HAL_GPIO_WritePin(AD9912_CSB_LO3_PORT, AD9912_CSB_LO3_PIN, GPIO_PIN_SET)
#define AD9912_CSB_LO3_LOW()		HAL_GPIO_WritePin(AD9912_CSB_LO3_PORT, AD9912_CSB_LO3_PIN, GPIO_PIN_RESET)
#define AD9912_UPDATA_LO3_HIGH()	HAL_GPIO_WritePin(AD9912_UPDATA_LO3_PORT, AD9912_UPDATA_LO3_PIN, GPIO_PIN_SET)
#define AD9912_UPDATA_LO3_LOW()		HAL_GPIO_WritePin(AD9912_UPDATA_LO3_PORT, AD9912_UPDATA_LO3_PIN, GPIO_PIN_RESET)

//#define AD9912_SDO_LO3				PBin(9)
//#define	AD9912_RESET_LO3			PEout(4)
//#define	AD9912_PWRDOWN_LO3			PEout(5)

//函数声明
void AD9912_IO_Init_LO3(void);
void AD9912_Upfreq_LO3(double freq);
void AD9912_Init_LO3();

#endif
