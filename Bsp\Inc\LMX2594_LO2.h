#ifndef __LMX2594_LO2_H__
#define __LMX2594_LO2_H__

#include "stm32f1xx_hal.h"

#include "main.h"

//参考频率
#define LMX2594_RefFreKHz_LO2 		100000

//鉴相频率
#define LMX2594_FpdFreKHz_200MHz_LO2 			200000
#define LMX2594_FpdFreKHz_100MHz_LO2 			100000
#define LMX2594_FpdFreKHz_75MHz_LO2 			75000
#define LMX2594_FpdFreKHz_50MHz_LO2 			50000
#define LMX2594_FpdFreKHz_40MHz_LO2 			40000

//鉴相整数倍
#define LMX2594_FreKHz_50M_LO2 						50000

//宏定义
// LMX2594 LO2 引脚定义
#define LMX2594_CLK_LO2_PIN		GPIO_PIN_15
#define LMX2594_CLK_LO2_PORT		GPIOD
#define LMX2594_SDI_LO2_PIN		GPIO_PIN_14
#define LMX2594_SDI_LO2_PORT		GPIOD
#define LMX2594_CS_LO2_PIN		GPIO_PIN_13
#define LMX2594_CS_LO2_PORT		GPIOD
#define LMX2594_LD_LO2_PIN		GPIO_PIN_12
#define LMX2594_LD_LO2_PORT		GPIOD

// LMX2594 LO2 操作宏
#define LMX2594_CLK_LO2_HIGH()		HAL_GPIO_WritePin(LMX2594_CLK_LO2_PORT, LMX2594_CLK_LO2_PIN, GPIO_PIN_SET)
#define LMX2594_CLK_LO2_LOW()		HAL_GPIO_WritePin(LMX2594_CLK_LO2_PORT, LMX2594_CLK_LO2_PIN, GPIO_PIN_RESET)
#define LMX2594_SDI_LO2_HIGH()		HAL_GPIO_WritePin(LMX2594_SDI_LO2_PORT, LMX2594_SDI_LO2_PIN, GPIO_PIN_SET)
#define LMX2594_SDI_LO2_LOW()		HAL_GPIO_WritePin(LMX2594_SDI_LO2_PORT, LMX2594_SDI_LO2_PIN, GPIO_PIN_RESET)
#define LMX2594_CS_LO2_HIGH()		HAL_GPIO_WritePin(LMX2594_CS_LO2_PORT, LMX2594_CS_LO2_PIN, GPIO_PIN_SET)
#define LMX2594_CS_LO2_LOW()		HAL_GPIO_WritePin(LMX2594_CS_LO2_PORT, LMX2594_CS_LO2_PIN, GPIO_PIN_RESET)
#define LMX2594_LD_LO2_READ()		HAL_GPIO_ReadPin(LMX2594_LD_LO2_PORT, LMX2594_LD_LO2_PIN)

//函数声明
void LMX2594_IO_Init_LO2(void);
void Write_Reg_LMX2594_LO2(uint8_t Addr, uint16_t data);
void Set_Reg_LMX2594_LO2(uint32_t FreKHz, uint32_t RefFreKHz);
void Set_Fre_LMX2594_LO2(uint32_t FreKHz, uint32_t RefFreKHz);
uint8_t	Check_LMX2594_LO2(void);

#endif
