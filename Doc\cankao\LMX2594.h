#ifndef __LMX2594_H__
#define __LMX2594_H__

#include "stm32f10x.h"

#include "sys.h"
#include "timer.h"

//参考频率
#define LMX2594_RefFreKHz_LO1 		100000

//鉴相频率
#define LMX2594_FpdFreKHz_200MHz_LO1 			200000
#define LMX2594_FpdFreKHz_100MHz_LO1 			100000
#define LMX2594_FpdFreKHz_75MHz_LO1 			75000
#define LMX2594_FpdFreKHz_50MHz_LO1 			50000
#define LMX2594_FpdFreKHz_40MHz_LO1 			40000

//鉴相整数倍
#define LMX2594_FreKHz_50M_LO1 						50000

//宏定义
#define LMX2594_CLK_LO1 	PCout(4)
#define LMX2594_SDI_LO1 	PEout(7)
#define LMX2594_CS_LO1 		PDout(13)
#define LMX2594_LD_LO1 		PAin(5)

#define LMX2594_CLK_LO2 	PCout(5)
#define LMX2594_SDI_LO2 	PBout(0)
#define LMX2594_CS_LO2 		PBout(1)
#define LMX2594_LD_LO2 		PAin(6)

//函数声明
void LMX2594_IO_Init_LO1(void);
void Write_Reg_LMX2594_LO1(uint8_t Addr, uint16_t data);
void Set_Reg_LMX2594_LO1(uint32_t FreKHz, uint32_t RefFreKHz);
void Set_Fre_LMX2594_LO1(uint32_t FreKHz, uint32_t RefFreKHz);
uint8_t	Check_LMX2594_LO1(void);

void LMX2594_IO_Init_LO2(void);
void Write_Reg_LMX2594_LO2(uint8_t Addr, uint16_t data);
void Set_Reg_LMX2594_LO2(uint32_t FreKHz, uint32_t RefFreKHz);
void Set_Fre_LMX2594_LO2(uint32_t FreKHz, uint32_t RefFreKHz);
uint8_t	Check_LMX2594_LO2(void);

#endif
