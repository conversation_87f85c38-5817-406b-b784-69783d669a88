/**
  ******************************************************************************
  * @file       : main.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 程序主体
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */

/* 头文件包含 ----------------------------------------------------------------*/
#include "main.h"

/* 变量定义 -------------------------------------------------------------------*/
uint8_t MCU_OSC;

/**
 * @brief  主函数
 * @param  无
 * @retval 无
 */
int main(void) {
    HAL_Init();                                 // HAL库初始化
    MCU_OSC = SystemClock_Config(RCC_PLL_MUL9); // 内部参考时64M主频，外部参考时：主频=晶振*传入参数
    MCU_OSC ? delay_init(72) : delay_init(64);  // 根据不同时钟主频初始化延时函数

    Usart1_Init(115200); // 串口初始化

    while (1) {
        if (USART_RX_STA & 0x8000) {
            USART_RX_STA = 0;
        }
    }
}

/*********************************************END OF FILE**********************/
