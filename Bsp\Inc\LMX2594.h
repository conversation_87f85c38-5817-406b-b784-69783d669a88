#ifndef __LMX2594_H__
#define __LMX2594_H__

#include "stm32f1xx_hal.h"

#include "main.h"

//参考频率
#define LMX2594_RefFreKHz_LO1 		100000

//鉴相频率
#define LMX2594_FpdFreKHz_200MHz_LO1 			200000
#define LMX2594_FpdFreKHz_100MHz_LO1 			100000
#define LMX2594_FpdFreKHz_75MHz_LO1 			75000
#define LMX2594_FpdFreKHz_50MHz_LO1 			50000
#define LMX2594_FpdFreKHz_40MHz_LO1 			40000

//鉴相整数倍
#define LMX2594_FreKHz_50M_LO1 						50000

//宏定义
// LMX2594 LO1 引脚定义
#define LMX2594_CLK_LO1_PIN		GPIO_PIN_9
#define LMX2594_CLK_LO1_PORT		GPIOC
#define LMX2594_SDI_LO1_PIN		GPIO_PIN_8
#define LMX2594_SDI_LO1_PORT		GPIOC
#define LMX2594_CS_LO1_PIN		GPIO_PIN_7
#define LMX2594_CS_LO1_PORT		GPIOC
#define LMX2594_LD_LO1_PIN		GPIO_PIN_6
#define LMX2594_LD_LO1_PORT		GPIOC

// LMX2594 LO1 操作宏
#define LMX2594_CLK_LO1_HIGH()		HAL_GPIO_WritePin(LMX2594_CLK_LO1_PORT, LMX2594_CLK_LO1_PIN, GPIO_PIN_SET)
#define LMX2594_CLK_LO1_LOW()		HAL_GPIO_WritePin(LMX2594_CLK_LO1_PORT, LMX2594_CLK_LO1_PIN, GPIO_PIN_RESET)
#define LMX2594_SDI_LO1_HIGH()		HAL_GPIO_WritePin(LMX2594_SDI_LO1_PORT, LMX2594_SDI_LO1_PIN, GPIO_PIN_SET)
#define LMX2594_SDI_LO1_LOW()		HAL_GPIO_WritePin(LMX2594_SDI_LO1_PORT, LMX2594_SDI_LO1_PIN, GPIO_PIN_RESET)
#define LMX2594_CS_LO1_HIGH()		HAL_GPIO_WritePin(LMX2594_CS_LO1_PORT, LMX2594_CS_LO1_PIN, GPIO_PIN_SET)
#define LMX2594_CS_LO1_LOW()		HAL_GPIO_WritePin(LMX2594_CS_LO1_PORT, LMX2594_CS_LO1_PIN, GPIO_PIN_RESET)
#define LMX2594_LD_LO1_READ()		HAL_GPIO_ReadPin(LMX2594_LD_LO1_PORT, LMX2594_LD_LO1_PIN)

//函数声明
void LMX2594_IO_Init_LO1(void);
void Write_Reg_LMX2594_LO1(uint8_t Addr, uint16_t data);
void Set_Reg_LMX2594_LO1(uint32_t FreKHz, uint32_t RefFreKHz);
void Set_Fre_LMX2594_LO1(uint32_t FreKHz, uint32_t RefFreKHz);
uint8_t	Check_LMX2594_LO1(void);

#endif
